// ============================================================================
// CODE STYLE VALIDATION FILE
// This file demonstrates all code style rules from docs/CODE_STYLES.md
// ============================================================================

// 1. IMPORT ORGANIZATION - Group imports in strict order
// Node.js built-in modules (with node: prefix)
import process from 'node:process'

// External libraries (alphabetical)

// Side-effect imports
import 'reflect-metadata'

// Internal modules (by proximity)
import type { DatabaseConfig } from '../config/types'

// 2. TYPE DEFINITIONS AND INTERFACES - PascalCase naming
// Separate interfaces instead of nested structures for reusability
interface DatabaseConnection {
    host: string
    password: string
    port: number
    username: string
}

interface UserProfile {
    email: string
    id: number
    name: string
    role: UserRole
}

// Example of extracted interfaces instead of nested structures
interface AppConfig {
    database: DatabaseConnection
    server: ServerConfig
}

interface ServerConfig {
    cors: boolean
    port: number
}

// Prefer interface over type for object shapes
interface ApiResponse<T> {
    data: T
    message: string
    success: boolean
}

// Union types for specific cases
type UserRole = 'admin' | 'moderator' | 'user'
type HttpMethod = 'DELETE' | 'GET' | 'PATCH' | 'POST' | 'PUT'

// Utility types for type manipulation
type PartialUser = Partial<UserProfile>
type UserEmail = Pick<UserProfile, 'email'>
type UserWithoutId = Omit<UserProfile, 'id'>

// 3. CONSTANTS AND CONFIGURATION - UPPERCASE_SNAKE_CASE
const MAX_RETRY_ATTEMPTS = 3
const DEFAULT_TIMEOUT = 5000
const API_BASE_URL = 'https://api.example.com'
const SUPPORTED_FILE_EXTENSIONS = ['.ts', '.js', '.json']

// Configuration object - proper spacing and trailing commas
const defaultConfig = {
    database: {
        host: 'localhost',
        port: 5432,
        ssl: false,
    },
    logging: {
        level: 'info',
        timestamp: true,
    },
    server: {
        cors: true,
        port: 3000,
    },
}

// Simple objects stay on single line
const simpleConfig = { enabled: true, timeout: 1000 }

// Arrays with proper formatting
const supportedMethods: HttpMethod[] = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']

const complexArray = [
    { id: 1, name: 'first', active: true },
    { id: 2, name: 'second', active: false },
    { id: 3, name: 'third', active: true },
]

// 4. IMPLEMENTATION - Functions and Classes

// Function naming: camelCase
function calculateUserScore(user: UserProfile): number {
    // Blank line before return statement
    return user.id * 10
}

// Arrow function with direct return for simple operations
const processUserData = (userData: string) => JSON.parse(userData)

// Empty arrow function - single line
const emptyCallback = () => {}

// Function with unused parameter - prefix with underscore
function handleError(_error: Error, message: string): void {
    console.error(message)
}

// Complex return type specified for clarity
function createApiClient(): Promise<{ get: (url: string) => Promise<unknown> }> {
    return Promise.resolve({
        get: async (url: string) => {
            const response = await fetch(url)

            return response.json()
        },
    })
}

// Simple return type - no explicit specification needed
function getUserName(user: UserProfile) {
    return user.name
}

// 5. CLASS STRUCTURE - Proper organization and formatting
class UserService {
    // Public properties first
    public readonly apiUrl: string
    public isInitialized: boolean

    // Protected properties with blank line separation
    protected connectionPool: unknown[]
    protected retryCount: number

    // Private properties - prefer # syntax for private fields
    #apiKey: string
    #internalCache: Map<string, unknown>

    // Constructor with inline access modifiers for few parameters
    public constructor(private readonly config: DatabaseConfig) {
        this.apiUrl = config.host
        this.isInitialized = false
        this.connectionPool = []
        this.retryCount = 0
        this.#apiKey = process.env.API_KEY || ''
        this.#internalCache = new Map()
    }

    // Static methods
    public static create(config: DatabaseConfig): UserService {
        return new UserService(config)
    }

    public static validateConfig(config: unknown): config is DatabaseConfig {
        return typeof config === 'object' && config !== null
    }

    // Public instance methods
    public async getUser(id: number): Promise<UserProfile | null> {
        try {
            const cachedUser = this.#getCachedUser(id)

            if (cachedUser) {
                return cachedUser
            }

            const user = await this.#fetchUserFromApi(id)
            this.#cacheUser(user)

            return user
        } catch (error) {
            this.#handleError(error)

            return null
        }
    }

    public initialize(): void {
        if (this.isInitialized) {
            return
        }

        this.#setupConnection()
        this.isInitialized = true
    }

    // Protected methods
    protected validateUser(user: unknown): user is UserProfile {
        return typeof user === 'object' && user !== null && 'id' in user && 'name' in user && 'email' in user
    }

    // Private methods using # syntax
    #getCachedUser(id: number): UserProfile | null {
        const cached = this.#internalCache.get(`user:${id}`)

        return cached as UserProfile | null
    }

    #cacheUser(user: UserProfile): void {
        this.#internalCache.set(`user:${user.id}`, user)
    }

    #fetchUserFromApi(id: number): Promise<UserProfile> {
        const url = `${this.apiUrl}/users/${id}`
        const options = { headers: { 'Authorization': `Bearer ${this.#apiKey}`, 'Content-Type': 'application/json' } }

        return fetch(url, options).then((response) => response.json())
    }

    #setupConnection(): void {
        // Connection setup logic
    }

    #handleError(error: unknown): void {
        if (error instanceof Error) {
            console.error('Service error:', error.message)
        } else {
            console.error('Unknown error:', error)
        }
    }
}

// 6. ERROR HANDLING PATTERNS

// Specific error catching with proper hierarchy
class ValidationError extends Error {
    public readonly code: string

    public constructor(message: string, code: string) {
        super(message)
        this.name = 'ValidationError'
        this.code = code
    }
}

class NetworkError extends Error {
    public readonly statusCode: number

    public constructor(message: string, statusCode: number) {
        super(message)
        this.name = 'NetworkError'
        this.statusCode = statusCode
    }
}

// Error handling function with specific error types
async function processUserRequest(userId: number): Promise<UserProfile> {
    // Input validation - fail fast
    if (userId <= 0) {
        throw new ValidationError('User ID must be positive', 'INVALID_ID')
    }

    try {
        const service = UserService.create(defaultConfig.database)
        service.initialize()

        const user = await service.getUser(userId)

        if (!user) {
            throw new NetworkError('User not found', 404)
        }

        return user
    } catch (error) {
        // Catch specific errors with useful information
        if (error instanceof ValidationError) {
            console.error(`Validation failed: ${error.message} (${error.code})`)
            throw error
        }

        if (error instanceof NetworkError) {
            console.error(`Network error: ${error.message} (${error.statusCode})`)
            throw error
        }

        // Handle unknown errors
        console.error('Unexpected error:', error)
        throw new Error('Internal server error')
    }
}

// 7. CONTROL STRUCTURES - Proper spacing and formatting

function demonstrateControlStructures(users: UserProfile[]): UserProfile[] {
    const activeUsers: UserProfile[] = []

    // If statement with proper spacing
    if (users.length === 0) {
        return activeUsers
    }

    // For loop with blank lines around
    for (const user of users) {
        if (user.role === 'admin') {
            activeUsers.push(user)
        }
    }

    // While loop with proper formatting
    let index = 0

    while (index < users.length) {
        const user = users[index]

        if (user.role !== 'user') {
            activeUsers.push(user)
        }

        index++
    }

    // Switch statement - no blank lines between cases
    const userRole = users[0]?.role

    switch (userRole) {
        case 'admin':
            console.log('Admin user detected')
            break
        case 'moderator':
            console.log('Moderator user detected')
            break
        case 'user':
            console.log('Regular user detected')
            break
        default:
            console.log('Unknown user role')
    }

    // Try-catch with proper spacing
    try {
        const processedUsers = users.map((user) => ({
            ...user,
            processed: true,
        }))

        return processedUsers
    } catch (error) {
        console.error('Processing failed:', error)

        return activeUsers
    }
}

// 8. UTILITY FUNCTIONS AND TYPE GUARDS

// Type guard function with single-line return
function isUserProfile(value: unknown): value is UserProfile {
    return (
        typeof value === 'object' &&
        value !== null &&
        'id' in value &&
        'name' in value &&
        'email' in value &&
        'role' in value
    )
}

// Utility function with descriptive naming
function formatUserDisplayName(user: UserProfile): string {
    return `${user.name} (${user.email})`
}

// Arrow function with object return for simple data transformation
const updateUserProfile = (
    userId: number,
    updates: Partial<Pick<UserProfile, 'name' | 'email'>>
): Promise<UserProfile> =>
    Promise.resolve({
        email: updates.email || '<EMAIL>',
        id: userId,
        name: updates.name || 'Default Name',
        role: 'user',
    })

// 9. EXPORTS - Alphabetically organized named exports on single lines
export {
    calculateUserScore,
    DEFAULT_TIMEOUT,
    demonstrateControlStructures,
    formatUserDisplayName,
    isUserProfile,
    MAX_RETRY_ATTEMPTS,
    NetworkError,
    processUserData,
    processUserRequest,
    updateUserProfile,
    UserService,
    ValidationError,
    type ApiResponse,
    type DatabaseConnection,
    type HttpMethod,
    type PartialUser,
    type UserProfile,
    type UserRole,
    type UserWithoutId,
}
